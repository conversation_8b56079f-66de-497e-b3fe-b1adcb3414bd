<template>
	<div
		class="modern-structure-tree"
		:class="{ collapsed: isCollapsed }"
	>
		<!-- 收起状态的简化内容 -->
		<div
			v-if="isCollapsed"
			class="collapsed-content"
		>
			<div class="collapsed-header">
				<el-icon class="collapsed-icon"><Grid /></el-icon>
			</div>
		</div>

		<!-- 展开状态的完整内容 -->
		<template v-else>
			<!-- 树形结构 -->
			<div
				class="tree-container"
				ref="treeContainerRef"
			>
				<el-tree-v2
					ref="treeRef"
					:data="treeData"
					:props="treeProps"
					:default-expand-all="false"
					:expand-on-click-node="false"
					@node-click="handleNodeClick"
					@node-expand="handleNodeExpand"
					@node-collapse="handleNodeCollapse"
					node-key="id"
					:height="treeHeight"
					:default-expanded-keys="expandedKeys"
					:item-size="30"
				>
					<template #default="{ node, data }">
						<div
							class="tree-node"
							:class="getNodeClass(data)"
						>
							<div class="node-icon-wrapper">
								<el-icon
									class="node-icon"
									:class="getIconClass(data)"
								>
									<component :is="getNodeIcon(data)" />
								</el-icon>
							</div>
							<div class="node-content">
								<span class="node-label">{{ data.label || '未命名节点' }}</span>
							</div>
							<div class="node-actions">
								<!-- 所有FDS相关节点的切换按钮 -->
								<template v-if="canToggleVisibility(data)">
									<el-button
										size="small"
										text
										@click.stop="toggleNodeVisibility(data)"
										:class="[
											'action-btn',
											getNodeHiddenStatus(data).isHidden
												? 'show-btn'
												: 'hide-btn',
										]"
										:title="
											getNodeHiddenStatus(data).isHidden ? '显示' : '隐藏'
										"
									>
										{{ getNodeHiddenStatus(data).isHidden ? '显示' : '隐藏' }}
									</el-button>
								</template>
							</div>
						</div>
					</template>
				</el-tree-v2>
			</div>
		</template>
	</div>
</template>

<script setup>
	import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue';
	import {
		User,
		Male,
		Female,
		House,
		Box,
		Grid,
		FolderOpened,
		Location,
	} from '@element-plus/icons-vue';

	const props = defineProps({
		personData: {
			type: Array,
			default: () => [],
		},
		fdsData: {
			type: Array,
			default: () => [],
		},
		collapsed: {
			type: Boolean,
			default: false,
		},
		selectedNode: {
			type: Object,
			default: null,
		},
		hiddenNodes: {
			type: Array,
			default: () => [],
		},
	});

	const emit = defineEmits(['node-click', 'toggle-node-visibility']);

	const treeRef = ref(null);
	const treeContainerRef = ref(null);
	const treeHeight = ref(1000);
	const expandedKeys = ref([]); // 展开的节点keys

	// 从父组件获取收起状态
	const isCollapsed = computed(() => props.collapsed);

	const treeProps = {
		children: 'children',
		label: 'label',
	};

	// 计算树结构数据
	const treeData = computed(() => {
		const tree = [];

		// 人员信息节点
		if (props.personData.length > 0) {
			const personNode = {
				id: 'persons',
				label: `人员信息 (${props.personData.length})`,
				type: 'persons_root',
				children: buildPersonTree(props.personData),
			};
			tree.push(personNode);
		}

		// FDS数据节点
		if (props.fdsData.length > 0) {
			const fdsNode = {
				id: 'fds',
				label: 'FDS对象',
				type: 'fds_root',
				children: buildFdsTree(props.fdsData),
			};
			tree.push(fdsNode);
		}

		return tree;
	});

	// 过滤节点方法
	const filterNode = (value, data) => {
		if (!value) return true;
		return data.label.toLowerCase().includes(value.toLowerCase());
	};

	// 构建人员树结构
	const buildPersonTree = (persons) => {
		const groups = {
			adult: [],
			child: [],
			elder: [],
		};

		// 按年龄分组，不再按性别分组
		persons.forEach((person, index) => {
			// 确保每个人员都有_id字段
			if (!person._id) {
				person._id = `person_${index}`;
			}
			const personWithIndex = { ...person, index };

			if (person.age < 18) {
				groups.child.push(personWithIndex);
			} else if (person.age >= 60) {
				groups.elder.push(personWithIndex);
			} else {
				groups.adult.push(personWithIndex);
			}
		});

		const result = [];

		// 构建分组节点，不再按性别分组
		Object.entries(groups).forEach(([ageGroup, persons]) => {
			const ageGroupLabel = {
				adult: '成年人',
				child: '儿童',
				elder: '老年人',
			}[ageGroup];

			if (persons.length > 0) {
				result.push({
					id: ageGroup,
					label: `${ageGroupLabel} (${persons.length})`,
					type: 'age_group',
					children: persons.map((person) => ({
						id: person._id, // 使用人员数据中的_id
						label: `${person.name || `人员${person.index + 1}`} (${
							person.age
						}岁)`,
						type: 'person',
						data: person,
						personIndex: person.index,
						gender: person.sex, // 添加性别信息用于图标颜色
					})),
				});
			}
		});

		return result;
	};

	// 构建FDS树结构
	const buildFdsTree = (fdsData) => {
		const groups = {};

		// 按ID名称前缀分组
		fdsData.forEach((item, index) => {
			if (!item.ID) {
				console.warn('FDS项缺少ID:', item);
				return;
			}

			// 使用已有的分组信息（如果有）
			let prefix;
			if (item.groupLabel) {
				prefix = item.groupLabel;
			} else {
				// 提取ID的前缀
				prefix = extractPrefix(item.ID);
			}

			if (!groups[prefix]) {
				groups[prefix] = [];
			}

			// 确保每个项目都有_id
			if (!item._id) {
				item._id = `fds_${Date.now()}_${index}`;
			}

			// 创建节点，使用引用而不是深拷贝，以优化性能
			const node = {
				id: item._id,
				label: item.ID,
				type: 'fds_object',
				// 只保存必要的引用，而不是整个对象的深拷贝
				data: item,
				fdsIndex: index,
				groupId: item.groupId,
				groupLabel: item.groupLabel || prefix,
			};

			groups[prefix].push(node);
		});

		// 构建分组节点
		const groupNodes = Object.entries(groups).map(([prefix, items]) => {
			return {
				id: `fds_group_${prefix}`,
				label: prefix,
				type: 'fds_group',
				children: items,
			};
		});

		return groupNodes;
	};

	// 提取ID前缀
	const extractPrefix = (id) => {
		// 如果ID为空，返回"其他"
		if (!id) return '其他';

		// 去除ID末尾的数字
		const baseId = id.replace(/\d+$/, '');

		// 如果去除数字后为空，直接返回原始ID
		if (!baseId.trim()) return id;

		// 返回去除末尾数字的前缀作为分组名
		return baseId;
	};

	// 获取节点图标
	const getNodeIcon = (data) => {
		switch (data.type) {
			case 'persons_root':
				return User;
			case 'age_group':
				return FolderOpened;
			case 'person':
				return User; // 统一使用User图标，通过颜色区分性别
			case 'fds_root':
				return House;
			case 'fds_group':
				return Grid;
			case 'fds_object':
				return Box;
			default:
				return Box;
		}
	};

	// 获取节点样式类
	const getNodeClass = (data) => {
		const classes = [`node-type-${data.type}`];

		if (isLeafNode(data)) {
			classes.push('leaf-node');
		}

		if (data.type === 'person' || data.type === 'fds_object') {
			classes.push('clickable-node');
		}

		// 检查是否为选中状态
		if (props.selectedNode && props.selectedNode.id === data.id) {
			classes.push('selected-node');
		}

		return classes;
	};

	// 获取图标样式类
	const getIconClass = (data) => {
		switch (data.type) {
			case 'persons_root':
				return 'icon-persons';
			case 'fds_root':
				return 'icon-fds';
			case 'person':
				// 根据性别设置不同颜色
				return data.gender === 'M' ? 'icon-person-male' : 'icon-person-female';
			case 'fds_object':
				return 'icon-object';
			default:
				return 'icon-folder';
		}
	};

	// 判断是否为叶子节点
	const isLeafNode = (data) => {
		return data.type === 'person' || data.type === 'fds_object';
	};

	// 判断节点是否可以切换可见性
	const canToggleVisibility = (data) => {
		return (
			data.type === 'fds_object' ||
			data.type === 'fds_group' ||
			data.type === 'fds_root' ||
			data.type === 'person' || // 单个人员节点
			data.type === 'age_group' || // 年龄组节点
			data.type === 'persons_root' // 人员根节点
		);
	};

	// 高性能状态管理 - 使用非响应式数据结构
	const hiddenNodesSet = new Set(); // 存储隐藏节点的ID
	const nodeStatusCache = new Map(); // 缓存节点状态计算结果
	let statusUpdateTrigger = ref(0); // 用于触发状态更新的响应式变量

	// 强制更新状态显示
	const forceUpdateStatus = () => {
		nodeStatusCache.clear();
		statusUpdateTrigger.value++;
	};

	// 同步外部隐藏状态变化
	const syncHiddenNodes = (hiddenNodeIds) => {
		hiddenNodesSet.clear();
		if (Array.isArray(hiddenNodeIds)) {
			hiddenNodeIds.forEach((id) => hiddenNodesSet.add(id));
		}
		forceUpdateStatus();
	};

	// 暴露方法和数据给父组件
	defineExpose({
		syncHiddenNodes,
		forceUpdateStatus,
		hiddenNodesSet,
	});

	// 获取节点隐藏状态（高性能版本）
	const getNodeHiddenStatus = (data) => {
		// 触发响应式依赖（但不使用响应式数据进行计算）
		statusUpdateTrigger.value; // 访问响应式变量以建立依赖

		const cacheKey = `${data.type}_${data.id}`;

		// 检查缓存
		if (nodeStatusCache.has(cacheKey)) {
			return nodeStatusCache.get(cacheKey);
		}

		let result = { isHidden: false, statusText: '' };

		if (data.type === 'fds_object') {
			// 单个FDS对象
			const isHidden = hiddenNodesSet.has(data.id);
			result = {
				isHidden,
				statusText: isHidden ? '已隐藏' : '',
			};
		} else if (data.type === 'fds_group') {
			// FDS分组 - 检查组内对象状态
			const groupObjects = collectLeafNodes([data]);
			const hiddenCount = groupObjects.filter((obj) =>
				hiddenNodesSet.has(obj.id)
			).length;
			const totalCount = groupObjects.length;

			if (hiddenCount === 0) {
				result = { isHidden: false, statusText: '' };
			} else if (hiddenCount === totalCount) {
				result = { isHidden: true, statusText: '全部隐藏' };
			} else {
				result = {
					isHidden: true,
					statusText: `${hiddenCount}/${totalCount}隐藏`,
				};
			}
		} else if (data.type === 'fds_root') {
			// FDS根节点 - 检查所有FDS对象状态
			const allFdsObjects = collectLeafNodes([data]);
			const hiddenCount = allFdsObjects.filter((obj) =>
				hiddenNodesSet.has(obj.id)
			).length;
			const totalCount = allFdsObjects.length;

			if (hiddenCount === 0) {
				result = { isHidden: false, statusText: '' };
			} else if (hiddenCount === totalCount) {
				result = { isHidden: true, statusText: '全部隐藏' };
			} else {
				result = {
					isHidden: true,
					statusText: `${hiddenCount}/${totalCount}隐藏`,
				};
			}
		}

		// 缓存结果
		nodeStatusCache.set(cacheKey, result);
		return result;
	};

	// 收集叶子节点
	const collectLeafNodes = (nodes) => {
		const leafNodes = [];
		if (!nodes || !Array.isArray(nodes)) return leafNodes;

		nodes.forEach((node) => {
			if (node.type === 'fds_object') {
				leafNodes.push(node);
			} else if (node.children && node.children.length > 0) {
				leafNodes.push(...collectLeafNodes(node.children));
			}
		});
		return leafNodes;
	};

	// 切换节点可见性（高性能版本）
	const toggleNodeVisibility = (data) => {
		if (!canToggleVisibility(data)) return;

		if (data.type === 'fds_object') {
			// 单个对象切换
			const isCurrentlyHidden = hiddenNodesSet.has(data.id);
			if (isCurrentlyHidden) {
				hiddenNodesSet.delete(data.id);
			} else {
				hiddenNodesSet.add(data.id);
			}
		} else if (data.type === 'person') {
			// 人员节点切换
			const isCurrentlyHidden = hiddenNodesSet.has(data.id);
			if (isCurrentlyHidden) {
				hiddenNodesSet.delete(data.id);
			} else {
				hiddenNodesSet.add(data.id);
			}
		} else if (data.type === 'age_group') {
			// 年龄组切换 - 切换组内所有人员
			const groupPersons = collectLeafNodes([data]);

			// 获取当前年龄组的状态
			const currentStatus = getNodeHiddenStatus(data);

			// 如果当前显示为"隐藏"状态（即按钮显示"显示"），则执行显示操作
			// 如果当前显示为"显示"状态（即按钮显示"隐藏"），则执行隐藏操作
			const shouldHide = !currentStatus.isHidden;

			groupPersons.forEach((person) => {
				if (shouldHide) {
					hiddenNodesSet.add(person.id);
				} else {
					hiddenNodesSet.delete(person.id);
				}
			});
		} else if (data.type === 'persons_root') {
			// 人员根节点切换 - 切换所有人员
			const allPersons = collectLeafNodes([data]);

			// 获取当前根节点的状态
			const currentStatus = getNodeHiddenStatus(data);

			// 如果当前显示为"隐藏"状态（即按钮显示"显示"），则执行显示操作
			// 如果当前显示为"显示"状态（即按钮显示"隐藏"），则执行隐藏操作
			const shouldHide = !currentStatus.isHidden;

			allPersons.forEach((person) => {
				if (shouldHide) {
					hiddenNodesSet.add(person.id);
				} else {
					hiddenNodesSet.delete(person.id);
				}
			});
		} else if (data.type === 'fds_group') {
			// 分组切换 - 切换组内所有对象
			const groupObjects = collectLeafNodes([data]);

			// 获取当前分组的状态
			const currentStatus = getNodeHiddenStatus(data);

			// 如果当前显示为"隐藏"状态（即按钮显示"显示"），则执行显示操作
			// 如果当前显示为"显示"状态（即按钮显示"隐藏"），则执行隐藏操作
			const shouldHide = !currentStatus.isHidden;

			groupObjects.forEach((obj) => {
				if (shouldHide) {
					hiddenNodesSet.add(obj.id);
				} else {
					hiddenNodesSet.delete(obj.id);
				}
			});
		} else if (data.type === 'fds_root') {
			// 根节点切换 - 切换所有FDS对象
			const allFdsObjects = collectLeafNodes([data]);

			// 获取当前根节点的状态
			const currentStatus = getNodeHiddenStatus(data);

			// 如果当前显示为"隐藏"状态（即按钮显示"显示"），则执行显示操作
			// 如果当前显示为"显示"状态（即按钮显示"隐藏"），则执行隐藏操作
			const shouldHide = !currentStatus.isHidden;

			allFdsObjects.forEach((obj) => {
				if (shouldHide) {
					hiddenNodesSet.add(obj.id);
				} else {
					hiddenNodesSet.delete(obj.id);
				}
			});
		}

		// 强制更新状态显示
		forceUpdateStatus();

		// 发出事件给父组件处理3D场景
		emit('toggle-node-visibility', data);
	};

	// 处理节点点击
	const handleNodeClick = (data) => {
		// 只有叶子节点才发射点击事件
		// 非叶子节点的展开/收起由el-tree-v2自动处理
		if (isLeafNode(data)) {
			emit('node-click', data);
		}
	};

	// 处理节点展开
	const handleNodeExpand = (data) => {
		if (!expandedKeys.value.includes(data.id)) {
			expandedKeys.value.push(data.id);
		}
	};

	// 处理节点收起
	const handleNodeCollapse = (data) => {
		const index = expandedKeys.value.indexOf(data.id);
		if (index > -1) {
			expandedKeys.value.splice(index, 1);
		}
	};

	// 处理展开键更新
	const handleExpandedKeysUpdate = (keys) => {
		expandedKeys.value = keys;
		console.log('展开键更新:', keys);
	};

	// 定位节点
	const locateNode = (data) => {
		emit('node-click', data);
	};

	// 计算树容器高度
	const calculateTreeHeight = () => {
		if (treeContainerRef.value) {
			const rect = treeContainerRef.value.getBoundingClientRect();
			const containerHeight = treeContainerRef.value.offsetHeight;
			// 减去一些padding和边距
			treeHeight.value = Math.max(containerHeight - 20, 200);
		}
	};

	// 窗口大小变化时重新计算高度
	const handleResize = () => {
		nextTick(() => {
			calculateTreeHeight();
		});
	};

	// 监听props变化
	watch([() => props.personData, () => props.fdsData], () => {
		// 数据变化后重新计算高度
		nextTick(() => {
			calculateTreeHeight();
		});
	});

	// 监听收起状态变化
	watch(isCollapsed, () => {
		if (!isCollapsed.value) {
			nextTick(() => {
				calculateTreeHeight();
			});
		}
	});

	// 监听selectedNode变化，自动展开和滚动到选中节点
	watch(
		() => props.selectedNode,
		(newNode, oldNode) => {
			if (newNode) {
				expandToNode(newNode);
			}
		}
	);

	// 展开到指定节点
	const expandToNode = (nodeData) => {
		if (!nodeData) return;

		// 根据节点类型确定需要展开的父节点
		const keysToExpand = [];

		if (nodeData.type === 'person') {
			// 人员节点：需要展开 persons -> age_group
			keysToExpand.push('persons');

			// 根据年龄确定年龄组
			const person = nodeData.data;
			let ageGroup;
			if (person && person.age) {
				if (person.age < 18) {
					ageGroup = 'child';
				} else if (person.age >= 60) {
					ageGroup = 'elder';
				} else {
					ageGroup = 'adult';
				}
				keysToExpand.push(ageGroup);
			}
		} else if (nodeData.type === 'fds_object') {
			// FDS对象节点：需要展开 fds -> fds_group
			keysToExpand.push('fds');

			// 优先使用已有的分组信息
			if (nodeData.groupId) {
				keysToExpand.push(nodeData.groupId);
			}
			// 如果没有分组ID但有数据和ID，尝试提取前缀
			else if (nodeData.data && nodeData.data.ID) {
				const prefix = extractPrefix(nodeData.data.ID);
				const groupId = `fds_group_${prefix}`;
				keysToExpand.push(groupId);
			}
		}

		// 更新展开的keys
		if (keysToExpand.length > 0) {
			console.log('展开节点到:', keysToExpand);
			const newKeys = [...new Set([...expandedKeys.value, ...keysToExpand])];
			expandedKeys.value = newKeys;
		}
	};

	onMounted(() => {
		// 初始化时计算高度
		nextTick(() => {
			calculateTreeHeight();
		});

		// 设置默认展开的节点
		expandedKeys.value = ['persons', 'fds'];

		// 监听窗口大小变化
		window.addEventListener('resize', handleResize);
	});

	onUnmounted(() => {
		window.removeEventListener('resize', handleResize);
	});
</script>

<style scoped>
	.modern-structure-tree {
		height: 100%;
		display: flex;
		flex-direction: column;
		background: var(--app-surface-1);
		overflow: hidden;
	}

	/* ===== 收起状态样式 ===== */
	.modern-structure-tree.collapsed {
		width: 60px;
	}

	.collapsed-content {
		height: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-top: var(--app-space-lg);
	}

	.collapsed-header {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 40px;
		height: 40px;
		background: var(--app-surface-2);
		border-radius: var(--app-radius-medium);
		border: 1px solid var(--app-border-primary);
	}

	.collapsed-icon {
		font-size: 20px;
		color: var(--app-primary);
	}

	/* ===== 树容器 ===== */
	.tree-container {
		flex: 1;
		overflow: auto;
		padding: var(--app-space-sm);
		background-color: var(--app-bg-secondary);
		border-radius: var(--app-radius-medium);
		box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.05);
	}

	.modern-tree {
		background: transparent;
	}

	/* ===== 树节点样式 ===== */
	.tree-node {
		display: flex;
		align-items: center;
		width: 100%;
		padding: var(--app-space-xs) var(--app-space-sm);
		min-height: 30px;
		position: relative;
	}

	.node-icon-wrapper {
		display: flex;
		align-items: center;
		margin-right: var(--app-space-sm);
	}

	.node-icon {
		font-size: 16px;
		color: var(--app-text-secondary);
	}

	.icon-persons {
		color: var(--app-primary);
	}
	.icon-fds {
		color: var(--app-warning);
	}
	.icon-person-male {
		color: #409eff; /* 蓝色代表男性 */
	}
	.icon-person-female {
		color: #f56c6c; /* 粉红色代表女性 */
	}
	.icon-object {
		color: var(--app-info);
	}
	.icon-folder {
		color: var(--app-text-tertiary);
	}

	.node-content {
		flex: 1;
		display: flex;
		align-items: center;
		gap: var(--app-space-sm);
	}

	.node-label {
		font-size: var(--app-font-size-sm);
		color: var(--app-text-primary);
		font-weight: var(--app-font-weight-normal);
	}

	.node-actions {
		display: flex;
		align-items: center;
		opacity: 0;
		transition: opacity var(--app-duration-normal);
	}

	.tree-node:hover .node-actions {
		opacity: 1;
	}

	.action-btn {
		padding: var(--app-space-xs);
		border-radius: 4px;
		margin-left: 2px;
		color: var(--app-primary);
		display: flex;
		align-items: center;
		gap: 4px;
	}

	.hide-btn {
		color: var(--app-warning);
	}

	.show-btn {
		color: var(--app-success);
	}

	.hide-btn:hover {
		color: var(--app-warning);
		background-color: rgba(250, 173, 20, 0.1);
	}

	.show-btn:hover {
		color: var(--app-success);
		background-color: rgba(82, 196, 26, 0.1);
	}

	/* ===== 选中状态样式 ===== */
	.selected-node .node-label {
		color: var(--app-text-primary) !important;
		font-weight: var(--app-font-weight-semibold);
	}

	.selected-node .node-icon {
		color: var(--app-primary-dark) !important;
	}

	/* 移除highlighted-node样式 */

	/* Element Plus树组件样式覆盖 */
	:deep(.el-tree-node.is-current > .el-tree-node__content) {
		background-color: rgba(74, 144, 226, 0.15) !important;
	}

	/* 选中节点的文字样式 */
	:deep(.el-tree-node.is-current .node-label) {
		color: var(--app-primary) !important;
		font-weight: var(--app-font-weight-medium);
	}

	:deep(.el-tree-node__content:hover) {
		background-color: rgba(0, 0, 0, 0.05) !important;
	}

	:deep(.el-tree-node.is-current > .el-tree-node__content:hover) {
		background-color: rgba(74, 144, 226, 0.2) !important;
	}
</style>
