import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { SkeletonUtils } from 'three/examples/jsm/Addons.js';
import { globalLoading } from '../../composables/useLoading.js';
import models, { getRandomModelByType } from '../config/index.js';
import { v4 } from 'uuid';

/**
 * 人物动画类型枚举
 */
const ANIMATION_TYPE = {
	IDLE: 0, // 静止状态
	WALK_NORMAL: 1, // 正常行走
	WALK_SMALL: 2, // 小步伐行走
	STAIR_UP: 3, // 上楼梯
	STAIR_DOWN: 4, // 下楼梯
	STAIR_UP_SMALL: 5, // 上楼梯小步伐
	STAIR_DOWN_SMALL: 6, // 下楼梯小步伐
};

/**
 * 人物状态码枚举
 */
const CHARACTER_STATUS = {
	GROUND_WALK_NORMAL: 1, // 平地正常行走
	GROUND_WALK_SMALL: 2, // 平地小步伐
	GROUND_IDLE: 3, // 平地静止
	STAIR_UP_NORMAL: 4, // 上楼梯正常
	STAIR_UP_SMALL: 5, // 上楼梯小步伐
	STAIR_UP_IDLE: 6, // 上楼梯静止
	STAIR_DOWN_NORMAL: 7, // 下楼梯正常
	STAIR_DOWN_SMALL: 8, // 下楼梯小步伐
	STAIR_DOWN_IDLE: 9, // 下楼梯静止
};

/**
 * 人物动画状态映射
 * 将状态码映射到对应的动画类型
 */
const ANIMATION_STATUS_MAP = {
	[CHARACTER_STATUS.GROUND_WALK_NORMAL]: ANIMATION_TYPE.WALK_NORMAL, // 平地正常 -> 正常行走动画
	[CHARACTER_STATUS.GROUND_WALK_SMALL]: ANIMATION_TYPE.WALK_SMALL, // 平地小步伐 -> 小步伐行走动画
	[CHARACTER_STATUS.GROUND_IDLE]: ANIMATION_TYPE.IDLE, // 平地静止 -> 静止状态
	[CHARACTER_STATUS.STAIR_UP_NORMAL]: ANIMATION_TYPE.STAIR_UP, // 上楼梯正常 -> 上楼梯动画
	[CHARACTER_STATUS.STAIR_UP_SMALL]: ANIMATION_TYPE.STAIR_UP_SMALL, // 上楼梯小步伐 -> 上楼梯小步伐动画
	[CHARACTER_STATUS.STAIR_UP_IDLE]: ANIMATION_TYPE.IDLE, // 上楼梯静止 -> 静止状态
	[CHARACTER_STATUS.STAIR_DOWN_NORMAL]: ANIMATION_TYPE.STAIR_DOWN, // 下楼梯正常 -> 下楼梯动画
	[CHARACTER_STATUS.STAIR_DOWN_SMALL]: ANIMATION_TYPE.STAIR_DOWN_SMALL, // 下楼梯小步伐 -> 下楼梯小步伐动画
	[CHARACTER_STATUS.STAIR_DOWN_IDLE]: ANIMATION_TYPE.IDLE, // 下楼梯静止 -> 静止状态
};

/**
 * 人物管理器
 * 负责人物模型的加载、动画控制、移动数据处理等
 */
export class CharacterManager {
	constructor(viewEngine) {
		// 核心引用
		this.viewEngine = viewEngine;

		// 人物相关数据
		this.characters = [];
		this.personInfo = [];
		this.characterMaterials = [];

		// 动画相关
		this.animationMixers = [];
		this.animationActions = [];
		this.characterStates = [];
		this.modelAnimations = new Map();

		// 移动和位置数据
		this.moveData = [];
		this.targetPositions = [];
		this.targetRotations = [];
		this.previousPositions = [];

		// 播放控制
		this.isPlaying = false;
		this.timeIndex = 0;
		this.lastUpdateTime = 0;
		this.updateInterval = 500; // 0.5秒一帧
		this.frameRate = 2; // 每秒2帧
		this.lerpFactor = 0.05;

		// 资源管理
		this.textureLoader = new THREE.TextureLoader();
		this.models = new Map();
		this.textures = new Map();
	}

	// ==================== 资源加载方法 ====================

	/**
	 * 加载贴图资源
	 * @param {Object} textureConfig - 贴图配置对象
	 * @returns {Promise} 加载完成的Promise
	 */
	async loadTexture(textureConfig) {
		return new Promise((resolve) => {
			// 如果贴图已存在，直接返回
			if (this.textures.has(textureConfig.name)) {
				resolve();
				return;
			}

			this.textureLoader.load(`./textures/${textureConfig.path}`, (texture) => {
				this._configureTexture(texture);
				this.textures.set(textureConfig.name, texture);
				resolve();
			});
		});
	}

	/**
	 * 配置贴图参数
	 * @param {THREE.Texture} texture - 要配置的贴图
	 */
	_configureTexture(texture) {
		texture.wrapS = THREE.RepeatWrapping;
		texture.wrapT = THREE.RepeatWrapping;
		texture.flipY = false; // GLB模型通常需要设置为false
		texture.colorSpace = THREE.SRGBColorSpace;
		texture.magFilter = THREE.LinearFilter;
		texture.minFilter = THREE.LinearMipmapLinearFilter;
		texture.generateMipmaps = true;
	}

	/**
	 * 为模型应用贴图和材质
	 * @param {THREE.Object3D} model - 要应用材质的模型
	 * @param {Array<THREE.Texture>} textures - 贴图数组
	 */
	applyTextureToModel(model, textures) {
		let textureIndex = 0;

		model.traverse((child) => {
			if (this._shouldApplyMaterial(child, textureIndex)) {
				const material = this._createCharacterMaterial(
					textures[textureIndex++]
				);
				this._applyMaterialToMesh(child, material);
			}
		});
	}

	/**
	 * 检查是否应该为网格应用材质
	 */
	_shouldApplyMaterial(child, textureIndex) {
		return child.isMesh && child.material && textureIndex < 2;
	}

	/**
	 * 创建人物材质
	 * @param {THREE.Texture} texture - 贴图
	 * @returns {THREE.MeshStandardMaterial} 创建的材质
	 */
	_createCharacterMaterial(texture) {
		const material = new THREE.MeshStandardMaterial({
			map: texture,
			roughness: 0.9, // 更高的粗糙度，与FDS模型形成对比
			metalness: 0.0, // 完全非金属材质
			transparent: false,
			// 增强人物材质的亮度
			color: new THREE.Color(1.2, 1.2, 1.2), // 提高基础亮度
		});

		material.toneMapped = true;
		return material;
	}

	/**
	 * 将材质应用到网格
	 * @param {THREE.Mesh} mesh - 目标网格
	 * @param {THREE.Material} material - 要应用的材质
	 */
	_applyMaterialToMesh(mesh, material) {
		mesh.material = material;
		mesh.castShadow = true;
		mesh.receiveShadow = true;

		// 将材质添加到管理列表
		this.characterMaterials.push(material);
	}

	async loadModel(model) {
		return new Promise((resolve, reject) => {
			const loader = new GLTFLoader();
			if (this.models.has(model.name)) {
				resolve();
				return;
			}

			if (import.meta.env.DEV) {
				loader.load(`./glb/${model.name}.glb`, (gltf) => {
					// 存储每个模型自己的animations
					this.modelAnimations.set(model.name, gltf.animations);

					this.models.set(model.name, gltf.scene);
					resolve();
				});
			} else {
				window.Channel.loadGLB(`${model.name}.glb`)
					.then((gltf) => {
						// 存储每个模型自己的animations
						this.modelAnimations.set(model.name, gltf.animations);
						this.models.set(model.name, gltf.scene);
						resolve();
					})
					.catch(() => {
						resolve();
					});
			}
		});
	}

	async loadPersonInfo() {
		return new Promise((resolve, reject) => {
			if (import.meta.env.DEV) {
				fetch('./data/person_property.json').then((res) => {
					res.json().then((data) => {
						this.personInfo = data;
						resolve();
					});
				});
			} else {
				window.Channel.loadJSON().then((data) => {
					this.personInfo = data;
					resolve();
				});
			}
		});
	}

	createCharacters() {
		return new Promise(async (resolve, reject) => {
			const totalModels = models.length;
			let completedModels = 0;

			for (const model of models) {
				await this.loadModel(model);

				// 加载贴图
				for (const texture of model.texture) {
					await this.loadTexture(texture);
				}

				// 更新进度：从20%到60%，根据完成的模型数量
				completedModels++;
				const progress = 20 + (completedModels / totalModels) * 40;
				globalLoading.updateStepProgress(Math.round(progress));
			}
			resolve();
		});
	}
	async loadMoveData() {
		// 加载移动数据（在ViewEngine中已经设置为第5步的一部分，从60%开始）
		try {
			let data = [];
			if (import.meta.env.DEV) {
				const response = await fetch('./data/output.json');
				globalLoading.updateStepProgress(70);
				data = await response.json();
				globalLoading.updateStepProgress(80);
			} else {
				data = await window.Channel.loadMoveData((progress) => {
					// 将0-100的进度映射到60-80的范围
					const mappedProgress = 60 + progress * 0.2;
					globalLoading.updateStepProgress(mappedProgress);
				});
			}
			// 创建角色克隆
			const charactersCount = this.personInfo.length;

			Array.from({ length: charactersCount }).forEach((_, index) => {
				const sex = this.personInfo[index].sex;
				const age = this.personInfo[index].age;

				// 根据性别和年龄判断模型类型
				let modelType;
				if (age < 18) {
					modelType = sex === 'M' ? 'childMale' : 'childFemale';
				} else if (age >= 60) {
					modelType = sex === 'M' ? 'elderMale' : 'elderFemale';
				} else {
					modelType = sex === 'M' ? 'adultMale' : 'adultFemale';
				}

				// modelType = 'childFemale';
				const model = getRandomModelByType(modelType);

				const cloneModel = SkeletonUtils.clone(this.models.get(model.model));

				// cloneModel.position.set(10 + index * 0.5, 10, 10);
				const min = 1.3;
				const max = 1.8;
				const randomHeight = min + Math.random() * (max - min);
				const scale = randomHeight / 1.7;
				cloneModel.scale.multiplyScalar(scale);

				const textures = model.texture.map((texture) => {
					return this.textures.get(texture.name);
				});
				this.applyTextureToModel(cloneModel, textures);

				const mixer = new THREE.AnimationMixer(cloneModel);

				// 使用对应模型的animations
				const modelAnimations = this.modelAnimations.get(model.model);
				const actions = {};
				modelAnimations.forEach((anim, animIndex) => {
					const action = mixer.clipAction(anim);
					actions[animIndex] = action;
				});
				this.animationActions[index] = actions;
				this.animationMixers.push(mixer);

				// 添加对象信息用于识别
				cloneModel.userData = {
					type: 'character',
					personData: this.personInfo[index],
					_id: this.personInfo[index]._id, // 使用人员数据中的ID
				};

				// 添加到可点击对象列表
				this.viewEngine.clickableObjects.push(cloneModel);

				// 添加到ViewEngine的人员索引映射
				this.viewEngine.characterIndexMap.set(
					this.personInfo[index]._id,
					cloneModel
				);

				this.characters.push(cloneModel);
				this.viewEngine.scene.add(cloneModel);

				// 更新克隆进度
				const cloneProgress = 80 + ((index + 1) / charactersCount) * 15;
				globalLoading.updateStepProgress(cloneProgress);
			});

			this.moveData = data;

			// 自动优化人物亮度
			this.optimizeCharacterBrightness();

			// 不更新进度，让ViewEngine统一管理

			// 通知ViewEngine更新状态显示
			if (this.viewEngine.updatePlaybackStatus) {
				this.viewEngine.updatePlaybackStatus();
			}

			// 不需要调用nextStep，因为这是ViewEngine第5步的一部分
			return data;
		} catch (error) {
			console.error('移动数据加载失败:', error);
			throw error;
		}
	}
	async load() {
		await this.createCharacters();
		await this.loadMoveData();
	}

	normalizeAngleDiff(angleDiff) {
		return Math.atan2(Math.sin(angleDiff), Math.cos(angleDiff));
	}

	update(delta) {
		if (!this.isPlaying) {
			// 暂停时不更新动画混合器，保持当前动画帧
			return;
		}

		const currentTime = Date.now();
		const moveData = this.moveData;

		// 只在播放时更新动画混合器
		this.animationMixers.forEach((mixer) => mixer.update(delta));

		if (moveData.length === 0 || this.timeIndex >= moveData.length) {
			// 当没有移动数据或已到达数据末尾时，停止所有动画
			this.characters.forEach((_, index) => {
				if (this.animationActions[index]) {
					Object.values(this.animationActions[index]).forEach((action) => {
						action.stop();
					});
				}
			});
			return;
		}

		const shouldUpdateFrame =
			currentTime - this.lastUpdateTime >= this.updateInterval;

		if (shouldUpdateFrame && moveData[this.timeIndex]) {
			this.updateTargetData(moveData[this.timeIndex]);
			this.lastUpdateTime = currentTime;
			this.timeIndex++;
		}

		// 修复：使用当前timeIndex减1来获取当前帧数据，因为timeIndex在updateTargetData后已经+1了
		const currentFrameIndex = Math.max(0, this.timeIndex - 1);
		if (moveData[currentFrameIndex]) {
			this.updateCharacters(moveData[currentFrameIndex], delta);
		}
	}

	updateTargetData(frameData) {
		const items = Object.values(frameData)[0];
		const bounds = this.viewEngine.bounds;

		this.characters.forEach((character, index) => {
			const newPos = items[index];
			const position = new THREE.Vector3(
				newPos.x - bounds.centerX,
				newPos.z - bounds.centerZ,
				newPos.y - bounds.centerY
			);

			this.initializeCharacterData(index, position);

			// 只使用移动方向计算旋转
			const moveVector = position.clone().sub(this.previousPositions[index]);
			if (moveVector.length() > 0.001) {
				this.targetRotations[index] = Math.atan2(moveVector.x, moveVector.z);
			}

			this.targetPositions[index].copy(position);
			this.previousPositions[index].copy(position);
		});
	}

	initializeCharacterData(index, position) {
		if (!this.previousPositions[index]) {
			this.previousPositions[index] = position.clone();
		}
		if (!this.targetPositions[index]) {
			this.targetPositions[index] = new THREE.Vector3();
		}
		if (this.targetRotations[index] === undefined) {
			this.targetRotations[index] = 0;
		}
	}

	updateCharacters(frameData, delta) {
		const items = Object.values(frameData)[0];
		// 修复：当timeIndex为1时表示正在处理第0帧（因为已经+1了），这应该是第一帧
		const isFirstFrame = this.timeIndex <= 1;

		this.characters.forEach((character, index) => {
			// 如果没有对应的移动数据，停止该角色的所有动画
			if (!items[index] || !this.targetPositions[index]) {
				if (this.animationActions[index]) {
					Object.values(this.animationActions[index]).forEach((action) => {
						action.stop();
					});
				}
				return;
			}

			this.updateCharacterRotation(character, index, isFirstFrame, delta);

			this.updateCharacterPosition(character, index, isFirstFrame);

			this.updateCharacterAnimation(index, items[index].p);
		});
	}

	updateCharacterPosition(character, index, isFirstFrame) {
		if (isFirstFrame) {
			character.position.copy(this.targetPositions[index]);
		} else {
			character.position.lerp(this.targetPositions[index], this.lerpFactor);
		}
	}

	updateCharacterRotation(character, index, isFirstFrame, delta) {
		if (this.targetRotations[index] === undefined) return;

		if (isFirstFrame) {
			character.rotation.y = this.targetRotations[index];
		} else {
			const angleDiff = this.normalizeAngleDiff(
				this.targetRotations[index] - character.rotation.y
			);
			const rotationSpeed = 3.0;
			const maxRotation = rotationSpeed * delta;
			const clampedAngleDiff =
				Math.sign(angleDiff) * Math.min(Math.abs(angleDiff), maxRotation);
			character.rotation.y += clampedAngleDiff;
		}
	}

	// ==================== 动画控制方法 ====================

	/**
	 * 更新人物动画状态（带淡入淡出效果）
	 * @param {number} index - 人物索引
	 * @param {number} statusCode - 状态码
	 */
	updateCharacterAnimation(index, statusCode) {
		const animationType = ANIMATION_STATUS_MAP[statusCode];

		if (animationType === ANIMATION_TYPE.IDLE) {
			this._pauseCharacterAnimation(index);
		} else {
			this._playCharacterAnimation(index, animationType, true);
		}
	}

	/**
	 * 立即更新人物动画状态（无淡入淡出效果，用于跳转）
	 * @param {number} index - 人物索引
	 * @param {number} statusCode - 状态码
	 */
	updateCharacterAnimationImmediate(index, statusCode) {
		const animationType = ANIMATION_STATUS_MAP[statusCode];

		if (animationType === ANIMATION_TYPE.IDLE) {
			this._stopCharacterAnimation(index);
		} else {
			this._playCharacterAnimation(index, animationType, false);
		}
	}

	/**
	 * 暂停指定人物的动画
	 * @param {number} index - 人物索引
	 */
	_pauseCharacterAnimation(index) {
		if (this.characterStates[index]) {
			this.characterStates[index].paused = true;
		}
	}

	/**
	 * 停止指定人物的动画
	 * @param {number} index - 人物索引
	 */
	_stopCharacterAnimation(index) {
		if (this.characterStates[index]) {
			this.characterStates[index].stop();
		}
		this.characterStates[index] = null;
	}

	/**
	 * 播放指定人物的动画
	 * @param {number} index - 人物索引
	 * @param {number} animationType - 动画类型（来自ANIMATION_TYPE枚举）
	 * @param {boolean} useFade - 是否使用淡入淡出效果
	 */
	_playCharacterAnimation(index, animationType, useFade = true) {
		const action = this.animationActions[index][animationType];
		const currentState = this.characterStates[index];

		// 如果没有当前状态，直接播放新动画
		if (!currentState) {
			this.characterStates[index] = action;
			action.play();
			return;
		}

		// 如果是同一个动画且被暂停，恢复播放
		if (currentState === action && currentState.paused) {
			currentState.paused = false;
			currentState.play();
			return;
		}

		// 如果是不同的动画，切换动画
		if (currentState !== action) {
			this._switchAnimation(index, action, useFade);
			return;
		}

		// 如果是同一个动画且未暂停，确保正在运行
		if (
			currentState === action &&
			!currentState.paused &&
			!action.isRunning()
		) {
			action.play();
		}
	}

	/**
	 * 切换动画
	 * @param {number} index - 人物索引
	 * @param {THREE.AnimationAction} newAction - 新动画动作
	 * @param {boolean} useFade - 是否使用淡入淡出效果
	 */
	_switchAnimation(index, newAction, useFade) {
		const currentState = this.characterStates[index];

		if (useFade) {
			// 使用淡入淡出效果
			if (currentState) {
				currentState.fadeOut(0.2);
			}
			this.characterStates[index] = newAction;
			newAction.reset().fadeIn(0.2).play();
		} else {
			// 立即切换
			if (currentState) {
				currentState.stop();
			}
			this.characterStates[index] = newAction;
			newAction.reset().play();
		}
	}

	// ==================== 播放控制方法 ====================

	/**
	 * 开始播放动画
	 */
	play() {
		this.isPlaying = true;
		this.lastUpdateTime = Date.now();

		// 恢复所有暂停的动画
		this._resumeAllAnimations();

		// 如果是从头开始，立即处理第一帧
		if (this.timeIndex === 0 && this.moveData.length > 0) {
			this.updateTargetData(this.moveData[0]);
			this.timeIndex = 1;
		}
	}

	/**
	 * 暂停播放动画
	 */
	pause() {
		this.isPlaying = false;
		this._pauseAllAnimations();
	}

	/**
	 * 重置到初始状态
	 */
	reset() {
		this.isPlaying = false;
		this.timeIndex = 0;
		this.lastUpdateTime = 0;

		// 清空所有状态数据
		this._clearAllStates();

		// 重置所有人物到初始位置
		this._resetCharactersToInitialPosition();
	}

	/**
	 * 暂停所有动画
	 */
	_pauseAllAnimations() {
		this.characterStates.forEach((state) => {
			if (state && state.isRunning && state.isRunning()) {
				state.paused = true;
			}
		});
	}

	/**
	 * 恢复所有动画
	 */
	_resumeAllAnimations() {
		this.characterStates.forEach((state) => {
			if (state && state.paused) {
				state.paused = false;
				if (!state.isRunning()) {
					state.play();
				}
			}
		});
	}

	/**
	 * 清空所有状态数据
	 */
	_clearAllStates() {
		this.targetPositions = [];
		this.targetRotations = [];
		this.previousPositions = [];
		this.characterStates = [];
	}

	/**
	 * 重置所有人物到初始位置
	 */
	_resetCharactersToInitialPosition() {
		this.characters.forEach((character, index) => {
			if (this.moveData.length > 0 && this.moveData[0]) {
				const items = Object.values(this.moveData[0])[0];
				const bounds = this.viewEngine.bounds;
				const newPos = items[index];

				// 计算初始位置
				const position = new THREE.Vector3(
					newPos.x - bounds.centerX,
					newPos.z - bounds.centerZ,
					newPos.y - bounds.centerY
				);
				character.position.copy(position);

				// 重置旋转为默认方向
				character.rotation.y = 0;

				// 停止所有动画
				if (this.animationActions[index]) {
					Object.values(this.animationActions[index]).forEach((action) => {
						action.stop();
					});
				}
			}
		});
	}

	seekToFrame(frameIndex, isDragging = false) {
		// 确保frameIndex在有效范围内
		frameIndex = Math.max(0, Math.min(frameIndex, this.moveData.length - 1));

		// 暂停播放，防止在拖动时继续自动播放
		const wasPlaying = this.isPlaying;
		this.isPlaying = false;

		this.timeIndex = frameIndex;
		this.lastUpdateTime = Date.now();

		// 如果有数据，立即更新到指定帧
		if (this.moveData.length > 0 && this.moveData[frameIndex]) {
			// 清空之前的状态
			this.targetPositions = [];
			this.targetRotations = [];
			this.previousPositions = [];

			const items = Object.values(this.moveData[frameIndex])[0];
			const bounds = this.viewEngine.bounds;

			// 立即应用位置和旋转，不使用lerp
			this.characters.forEach((character, index) => {
				const newPos = items[index];

				// 设置位置
				const position = new THREE.Vector3(
					newPos.x - bounds.centerX,
					newPos.z - bounds.centerZ,
					newPos.y - bounds.centerY
				);
				character.position.copy(position);

				// 计算旋转 - 通过与前一帧比较来确定方向
				let rotation = character.rotation.y; // 保持当前旋转作为默认值

				if (frameIndex > 0 && this.moveData[frameIndex - 1]) {
					const prevItems = Object.values(this.moveData[frameIndex - 1])[0];
					const prevPos = prevItems[index];
					const prevPosition = new THREE.Vector3(
						prevPos.x - bounds.centerX,
						prevPos.z - bounds.centerZ,
						prevPos.y - bounds.centerY
					);

					const moveVector = position.clone().sub(prevPosition);
					if (moveVector.length() > 0.001) {
						rotation = Math.atan2(moveVector.x, moveVector.z);
					}
				}

				character.rotation.y = rotation;

				// 更新内部状态
				this.targetPositions[index] = position.clone();
				this.targetRotations[index] = rotation;
				this.previousPositions[index] = position.clone();

				// 更新动画状态 - 使用立即更新方法，不使用淡入淡出
				this.updateCharacterAnimationImmediate(index, newPos.p);
			});

			// 强制更新一次动画混合器来应用新的动画状态
			// 即使在暂停状态下也需要更新一次来显示正确的动画帧
			this.animationMixers.forEach((mixer) => mixer.update(0.016)); // 更新一帧（约60fps）
		}

		// 优化播放状态恢复逻辑
		if (wasPlaying && !isDragging) {
			// 如果不是拖拽操作，立即恢复播放状态，减少延迟
			this.isPlaying = true;
			this.lastUpdateTime = Date.now();
		} else if (wasPlaying && isDragging) {
			// 如果是拖拽操作，使用更短的延迟
			setTimeout(() => {
				if (!this.viewEngine.isDragging) {
					this.isPlaying = true;
					this.lastUpdateTime = Date.now();
				}
			}, 50); // 减少延迟从150ms到50ms
		}
	}

	// 添加获取时间的方法
	getTimeInfo() {
		const currentFrameIndex = this.timeIndex;
		const totalFrames = this.moveData.length;

		// 计算当前时间（秒）
		const currentTimeSeconds = currentFrameIndex / this.frameRate;
		// 计算总时间（秒）
		const totalTimeSeconds = totalFrames / this.frameRate;

		// 格式化为 分:秒 格式
		const formatTime = (seconds) => {
			const minutes = Math.floor(seconds / 60);
			const remainingSeconds = Math.floor(seconds % 60);
			return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
		};

		return {
			currentTime: formatTime(currentTimeSeconds),
			totalTime: formatTime(totalTimeSeconds),
			currentTimeSeconds,
			totalTimeSeconds,
		};
	}

	// ==================== 材质管理方法 ====================

	/**
	 * 设置人物材质颜色
	 * @param {number|string} color - 颜色值
	 */
	setCharacterColor(color) {
		this.characterMaterials.forEach((material) => {
			if (material && material.color) {
				material.color.set(color);
			}
		});
	}

	/**
	 * 设置人物材质透明度
	 * @param {number} opacity - 透明度值 (0-1)
	 */
	setCharacterOpacity(opacity) {
		this.characterMaterials.forEach((material) => {
			if (material) {
				material.transparent = opacity < 1.0;
				material.opacity = opacity;
			}
		});
	}

	/**
	 * 重置人物材质为默认设置
	 */
	resetCharacterMaterial() {
		this.characterMaterials.forEach((material) => {
			if (material && material.color) {
				material.color.set(0xffffff); // 白色
				material.transparent = false;
				material.opacity = 1.0;
			}
		});
	}

	/**
	 * 应用更自然的人物材质设置
	 */
	applyNaturalCharacterMaterial() {
		this.characterMaterials.forEach((material) => {
			if (material && material.color) {
				material.color.set(0xf0f0f0); // 稍微调暗，更自然
				material.transparent = false;
				material.opacity = 1.0;
			}
		});
	}

	/**
	 * 优化人物材质亮度
	 */
	optimizeCharacterBrightness() {
		this.characterMaterials.forEach((material) => {
			if (material) {
				// 提高材质的基础亮度
				material.color.set(1.5, 1.5, 1.5); // 提高到150%亮度

				// 如果是StandardMaterial，调整其他参数
				if (material.roughness !== undefined) {
					material.roughness = 0.7; // 稍微降低粗糙度，增加反射
					material.metalness = 0.0; // 确保非金属
				}

				material.transparent = false;
				material.opacity = 1.0;
				material.needsUpdate = true;
			}
		});
	}

	// ==================== 工具方法 ====================

	/**
	 * 获取时间信息
	 * @returns {Object} 包含当前时间和总时间的对象
	 */
	getTimeInfo() {
		const currentFrameIndex = this.timeIndex;
		const totalFrames = this.moveData.length;

		// 计算时间（秒）
		const currentTimeSeconds = currentFrameIndex / this.frameRate;
		const totalTimeSeconds = totalFrames / this.frameRate;

		return {
			currentTime: this._formatTime(currentTimeSeconds),
			totalTime: this._formatTime(totalTimeSeconds),
			currentTimeSeconds,
			totalTimeSeconds,
		};
	}

	/**
	 * 格式化时间为 分:秒 格式
	 * @param {number} seconds - 秒数
	 * @returns {string} 格式化的时间字符串
	 */
	_formatTime(seconds) {
		const minutes = Math.floor(seconds / 60);
		const remainingSeconds = Math.floor(seconds % 60);
		return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
	}

	/**
	 * 标准化角度差值
	 * @param {number} angleDiff - 角度差值
	 * @returns {number} 标准化后的角度差值
	 */
	normalizeAngleDiff(angleDiff) {
		return Math.atan2(Math.sin(angleDiff), Math.cos(angleDiff));
	}

	/**
	 * 获取动画类型的描述文本
	 * @param {number} animationType - 动画类型
	 * @returns {string} 动画类型的描述
	 */
	getAnimationTypeDescription(animationType) {
		const descriptions = {
			[ANIMATION_TYPE.IDLE]: '静止',
			[ANIMATION_TYPE.WALK_NORMAL]: '正常行走',
			[ANIMATION_TYPE.WALK_SMALL]: '小步伐行走',
			[ANIMATION_TYPE.STAIR_UP]: '上楼梯',
			[ANIMATION_TYPE.STAIR_DOWN]: '下楼梯',
			[ANIMATION_TYPE.STAIR_UP_SMALL]: '上楼梯小步伐',
			[ANIMATION_TYPE.STAIR_DOWN_SMALL]: '下楼梯小步伐',
		};
		return descriptions[animationType] || '未知动画';
	}

	/**
	 * 获取状态码的描述文本
	 * @param {number} statusCode - 状态码
	 * @returns {string} 状态码的描述
	 */
	getStatusCodeDescription(statusCode) {
		const descriptions = {
			[CHARACTER_STATUS.GROUND_WALK_NORMAL]: '平地正常行走',
			[CHARACTER_STATUS.GROUND_WALK_SMALL]: '平地小步伐',
			[CHARACTER_STATUS.GROUND_IDLE]: '平地静止',
			[CHARACTER_STATUS.STAIR_UP_NORMAL]: '上楼梯正常',
			[CHARACTER_STATUS.STAIR_UP_SMALL]: '上楼梯小步伐',
			[CHARACTER_STATUS.STAIR_UP_IDLE]: '上楼梯静止',
			[CHARACTER_STATUS.STAIR_DOWN_NORMAL]: '下楼梯正常',
			[CHARACTER_STATUS.STAIR_DOWN_SMALL]: '下楼梯小步伐',
			[CHARACTER_STATUS.STAIR_DOWN_IDLE]: '下楼梯静止',
		};
		return descriptions[statusCode] || '未知状态';
	}
}
